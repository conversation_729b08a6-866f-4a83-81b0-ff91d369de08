{"name": "proc-log", "version": "3.0.0", "files": ["bin/", "lib/"], "main": "lib/index.js", "description": "just emit 'log' events on the process object", "repository": {"type": "git", "url": "https://github.com/npm/proc-log.git"}, "author": "GitHub Inc.", "license": "ISC", "scripts": {"test": "tap", "snap": "tap", "posttest": "npm run lint", "postsnap": "eslint index.js test/*.js --fix", "lint": "eslint \"**/*.js\"", "postlint": "template-oss-check", "lintfix": "npm run lint -- --fix", "template-oss-apply": "template-oss-apply --force"}, "devDependencies": {"@npmcli/eslint-config": "^3.0.1", "@npmcli/template-oss": "4.5.1", "tap": "^16.0.1"}, "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.5.1"}, "tap": {"nyc-arg": ["--exclude", "tap-snapshots/**"]}}