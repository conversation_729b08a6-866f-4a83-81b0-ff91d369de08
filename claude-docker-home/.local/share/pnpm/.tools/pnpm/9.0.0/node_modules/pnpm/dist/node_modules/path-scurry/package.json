{"name": "path-scurry", "version": "1.10.1", "description": "walk paths fast and efficiently", "author": "<PERSON> <<EMAIL>> (https://blog.izs.me)", "main": "./dist/cjs/index.js", "module": "./dist/mjs/index.js", "exports": {".": {"import": {"types": "./dist/mjs/index.d.ts", "default": "./dist/mjs/index.js"}, "require": {"types": "./dist/cjs/index.d.ts", "default": "./dist/cjs/index.js"}}}, "files": ["dist"], "license": "BlueOak-1.0.0", "scripts": {"preversion": "npm test", "postversion": "npm publish", "prepublishOnly": "git push origin --follow-tags", "preprepare": "rm -rf dist", "prepare": "tsc -p tsconfig.json && tsc -p tsconfig-esm.json", "postprepare": "bash ./scripts/fixup.sh", "pretest": "npm run prepare", "presnap": "npm run prepare", "test": "c8 tap", "snap": "c8 tap", "format": "prettier --write . --loglevel warn", "typedoc": "typedoc --tsconfig tsconfig-esm.json ./src/*.ts", "bench": "bash ./scripts/bench.sh"}, "prettier": {"semi": false, "printWidth": 75, "tabWidth": 2, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSameLine": true, "arrowParens": "avoid", "endOfLine": "lf"}, "tap": {"coverage": false, "node-arg": ["--no-warnings", "--loader", "ts-node/esm"], "ts": false}, "devDependencies": {"@nodelib/fs.walk": "^1.2.8", "@types/node": "^20.1.4", "@types/tap": "^15.0.7", "c8": "^7.12.0", "eslint-config-prettier": "^8.6.0", "mkdirp": "^3.0.0", "prettier": "^2.8.3", "rimraf": "^5.0.1", "tap": "^16.3.4", "ts-node": "^10.9.1", "typedoc": "^0.23.24", "typescript": "^5.0.4"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "repository": {"type": "git", "url": "git+https://github.com/isaacs/path-scurry"}, "dependencies": {"lru-cache": "^9.1.1 || ^10.0.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"}}