[{"content": "Read the test log file to understand what tests are failing", "status": "completed", "priority": "high", "id": "1"}, {"content": "Read the test writing guidelines in apps/customer/tests/CLAUDE.md", "status": "completed", "priority": "high", "id": "2"}, {"content": "Examine working test examples (editor-ai-features.spec.ts and document-templates.spec.ts)", "status": "in_progress", "priority": "high", "id": "3"}, {"content": "Analyze the failing tests and identify root causes", "status": "pending", "priority": "high", "id": "4"}, {"content": "Fix the failing tests based on findings", "status": "pending", "priority": "high", "id": "5"}, {"content": "Run tests to verify fixes work", "status": "pending", "priority": "high", "id": "6"}, {"content": "Commit changes when all tests pass", "status": "pending", "priority": "medium", "id": "7"}]